import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import '../../../auth/domain/providers/auth_provider.dart';
import '../../../profile/presentation/screens/profile_screen.dart';
import '../../../workout/presentation/screens/workout_list_screen.dart';
import '../../../chat/presentation/screens/chat_screen.dart';
import '../../domain/providers/navigation_provider.dart';
import '../../../../core/theme/spacing.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../../shared/widgets/themed_components.dart';
import '../../../../core/animations/animation_utils.dart';
import '../../../dashboard/data/repositories/dashboard_repository.dart';
import '../../../dashboard/domain/models/today_workout.dart';
import '../../../dashboard/domain/providers/dashboard_provider.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);
    final selectedIndex = ref.watch(navigationIndexProvider);

    final screens = [
      const _HomeContent(),
      const WorkoutListScreen(),
      const ChatScreen(),
      const ProfileScreen(),
    ];

    return Scaffold(
      body: screens[selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: selectedIndex,
        onTap: (index) {
          ref.read(navigationIndexProvider.notifier).state = index;
        },
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.fitness_center),
            label: 'Workouts',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.chat_bubble_outline),
            label: 'Coach',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }
}

class _HomeContent extends ConsumerStatefulWidget {
  const _HomeContent();

  @override
  ConsumerState<_HomeContent> createState() => _HomeContentState();
}

class _HomeContentState extends ConsumerState<_HomeContent>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late List<Animation<double>> _cardAnimations;

  @override
  void initState() {
    super.initState();
    debugPrint('🏠 Home: _HomeContent initState called');
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _cardAnimations = AnimationUtils.createStaggeredAnimations(
      controller: _slideController,
      count: 4,
      staggerDelay: const Duration(milliseconds: 100),
    );
    
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final todayWorkoutAsync = ref.watch(todayWorkoutProvider);

    debugPrint('🏠 Home: Build called, provider state: ${todayWorkoutAsync.runtimeType}');

    return todayWorkoutAsync.when(
      data: (todayWorkout) {
        debugPrint('🏠 Home: Provider data received - workout: $todayWorkout');
        if (todayWorkout == null) {
          debugPrint('🏠 Home: Showing empty state');
          return _buildEmptyState(context);
        }
        debugPrint('🏠 Home: Showing home content');
        return _buildHomeContent(context, colorScheme, textTheme);
      },
      loading: () {
        debugPrint('🏠 Home: Provider still loading...');
        return _buildLoadingState(context);
      },
      error: (error, stack) {
        debugPrint('🏠 Home: Provider error: $error');
        return _buildHomeContent(context, colorScheme, textTheme); // Show home content on error
      },
    );
  }

  Widget _buildHomeContent(BuildContext context, ColorScheme colorScheme, TextTheme textTheme) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        title: AnimatedBuilder(
          animation: _fadeController,
          builder: (context, child) {
            return Opacity(
              opacity: _fadeController.value,
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      gradient: AppColorPalette.primaryGradient,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.fitness_center,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  AppSpacing.gapHorizontalMd,
                  Text(
                    'FitTracker',
                    style: textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        actions: [
          AnimatedBuilder(
            animation: _fadeController,
            builder: (context, child) {
              return Transform.scale(
                scale: _fadeController.value,
                child: Container(
                  margin: AppSpacing.paddingHorizontalMd,
                  child: GlassMorphismCard(
                    borderRadius: 12,
                    child: IconButton(
                      icon: Stack(
                        children: [
                          Icon(
                            Icons.notifications_outlined,
                            color: colorScheme.onSurface,
                          ),
                          Positioned(
                            right: 0,
                            top: 0,
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: AppColorPalette.error,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ],
                      ),
                      onPressed: () {
                        HapticFeedback.lightImpact();
                      },
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: AppSpacing.paddingLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section with Hero Animation
            AnimatedBuilder(
              animation: _cardAnimations[0],
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, 30 * (1 - _cardAnimations[0].value)),
                  child: Opacity(
                    opacity: _cardAnimations[0].value,
                    child: Container(
                      height: 220,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppColorPalette.primaryOrange,
                            AppColorPalette.primaryOrange.withOpacity(0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: AppSpacing.borderRadiusXl,
                        boxShadow: [
                          BoxShadow(
                            color: AppColorPalette.primaryOrange.withOpacity(0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Stack(
                        children: [
                          // Background pattern
                          Positioned(
                            right: -50,
                            top: -50,
                            child: Container(
                              width: 200,
                              height: 200,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white.withOpacity(0.1),
                              ),
                            ),
                          ),
                          Positioned(
                            left: -30,
                            bottom: -30,
                            child: Container(
                              width: 150,
                              height: 150,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white.withOpacity(0.05),
                              ),
                            ),
                          ),
                          // Content
                          Padding(
                            padding: AppSpacing.paddingLg,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          _getGreeting(),
                                          style: textTheme.headlineMedium?.copyWith(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                        AppSpacing.gapVerticalSm,
                                        Text(
                                          _getMotivationalText(),
                                          style: textTheme.bodyLarge?.copyWith(
                                            color: Colors.white.withOpacity(0.9),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Container(
                                      width: 60,
                                      height: 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.2),
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.wb_sunny,
                                        color: Colors.white,
                                        size: 30,
                                      ),
                                    ),
                                  ],
                                ),
                                const Spacer(),
                                Row(
                                  children: [
                                    _buildStreakBadge('3', 'Day Streak', Icons.local_fire_department),
                                    AppSpacing.gapHorizontalMd,
                                    _buildStreakBadge('1,250', 'Points', Icons.star),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
            AppSpacing.gapVerticalXl,
            
            // Today's Progress Section
            AnimatedBuilder(
              animation: _cardAnimations[1],
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, 30 * (1 - _cardAnimations[1].value)),
                  child: Opacity(
                    opacity: _cardAnimations[1].value,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "Today's Progress",
                              style: textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            TextButton(
                              onPressed: () {},
                              child: Text(
                                'View Details',
                                style: TextStyle(
                                  color: AppColorPalette.primaryOrange,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                        AppSpacing.gapVerticalMd,
                        Row(
                          children: [
                            Expanded(
                              child: _buildProgressCard(
                                icon: Icons.local_fire_department,
                                value: '850',
                                label: 'Calories',
                                progress: 0.65,
                                color: AppColorPalette.error,
                                gradient: LinearGradient(
                                  colors: [
                                    AppColorPalette.error,
                                    AppColorPalette.error.withOpacity(0.7),
                                  ],
                                ),
                              ),
                            ),
                            AppSpacing.gapHorizontalMd,
                            Expanded(
                              child: _buildProgressCard(
                                icon: Icons.directions_walk,
                                value: '8,234',
                                label: 'Steps',
                                progress: 0.82,
                                color: AppColorPalette.successGreen,
                                gradient: LinearGradient(
                                  colors: [
                                    AppColorPalette.successGreen,
                                    AppColorPalette.successGreen.withOpacity(0.7),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                        AppSpacing.gapVerticalMd,
                        Row(
                          children: [
                            Expanded(
                              child: _buildProgressCard(
                                icon: Icons.timer,
                                value: '45m',
                                label: 'Active Time',
                                progress: 0.45,
                                color: AppColorPalette.accentBlue,
                                gradient: LinearGradient(
                                  colors: [
                                    AppColorPalette.accentBlue,
                                    AppColorPalette.accentBlue.withOpacity(0.7),
                                  ],
                                ),
                              ),
                            ),
                            AppSpacing.gapHorizontalMd,
                            Expanded(
                              child: _buildProgressCard(
                                icon: Icons.favorite,
                                value: '125',
                                label: 'Heart Rate',
                                progress: 0.75,
                                color: Color(0xFF8B5CF6),
                                gradient: LinearGradient(
                                  colors: [
                                    Color(0xFF8B5CF6),
                                    Color(0xFF8B5CF6).withOpacity(0.7),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            AppSpacing.gapVerticalXl,
            
            // Recommended Workouts Section
            AnimatedBuilder(
              animation: _cardAnimations[2],
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, 30 * (1 - _cardAnimations[2].value)),
                  child: Opacity(
                    opacity: _cardAnimations[2].value,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Recommended for You',
                          style: textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        AppSpacing.gapVerticalMd,
                        SizedBox(
                          height: 180,
                          child: ListView.builder(
                            key: const ValueKey('recommended_workouts_list'),
                            scrollDirection: Axis.horizontal,
                            itemCount: 3,
                            itemBuilder: (context, index) {
                              final workouts = [
                                {
                                  'title': 'Full Body HIIT',
                                  'duration': '30 min',
                                  'difficulty': 'Intermediate',
                                  'icon': Icons.flash_on,
                                  'color': AppColorPalette.error,
                                },
                                {
                                  'title': 'Yoga Flow',
                                  'duration': '45 min',
                                  'difficulty': 'Beginner',
                                  'icon': Icons.self_improvement,
                                  'color': Color(0xFF8B5CF6),
                                },
                                {
                                  'title': 'Strength Training',
                                  'duration': '40 min',
                                  'difficulty': 'Advanced',
                                  'icon': Icons.fitness_center,
                                  'color': AppColorPalette.accentBlue,
                                },
                              ];
                              final workout = workouts[index];
                              
                              return Container(
                                key: ValueKey('recommended_workout_$index'),
                                width: 160,
                                margin: EdgeInsets.only(
                                  right: AppSpacing.md,
                                ),
                                child: GlassMorphismCard(
                                  borderRadius: 16,
                                  child: InkWell(
                                    onTap: () {
                                      HapticFeedback.lightImpact();
                                    },
                                    borderRadius: AppSpacing.borderRadiusLg,
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          width: double.infinity,
                                          height: 80,
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              colors: [
                                                (workout['color'] as Color),
                                                (workout['color'] as Color).withOpacity(0.7),
                                              ],
                                            ),
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(AppSpacing.radiusLg),
                                              topRight: Radius.circular(AppSpacing.radiusLg),
                                            ),
                                          ),
                                          child: Icon(
                                            workout['icon'] as IconData,
                                            color: Colors.white,
                                            size: 40,
                                          ),
                                        ),
                                        Padding(
                                          padding: AppSpacing.paddingMd,
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                workout['title'] as String,
                                                style: textTheme.titleMedium?.copyWith(
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              AppSpacing.gapVerticalXs,
                                              Text(
                                                workout['duration'] as String,
                                                style: textTheme.bodySmall,
                                              ),
                                              AppSpacing.gapVerticalXs,
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                  horizontal: AppSpacing.sm,
                                                  vertical: AppSpacing.xs,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: (workout['color'] as Color).withOpacity(0.1),
                                                  borderRadius: AppSpacing.borderRadiusSm,
                                                ),
                                                child: Text(
                                                  workout['difficulty'] as String,
                                                  style: textTheme.labelSmall?.copyWith(
                                                    color: workout['color'] as Color,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            AppSpacing.gapVerticalXl,
            
            // Quick Actions Section
            AnimatedBuilder(
              animation: _cardAnimations[3],
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, 30 * (1 - _cardAnimations[3].value)),
                  child: Opacity(
                    opacity: _cardAnimations[3].value,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Quick Actions',
                          style: textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        AppSpacing.gapVerticalMd,
                        Row(
                          children: [
                            Expanded(
                              child: _buildQuickActionCard(
                                icon: Icons.track_changes,
                                title: 'Track Activity',
                                color: AppColorPalette.primaryOrange,
                              ),
                            ),
                            AppSpacing.gapHorizontalMd,
                            Expanded(
                              child: _buildQuickActionCard(
                                icon: Icons.restaurant_menu,
                                title: 'Log Meal',
                                color: AppColorPalette.successGreen,
                              ),
                            ),
                          ],
                        ),
                        AppSpacing.gapVerticalMd,
                        Row(
                          children: [
                            Expanded(
                              child: _buildQuickActionCard(
                                icon: Icons.water_drop,
                                title: 'Water Intake',
                                color: AppColorPalette.accentBlue,
                              ),
                            ),
                            AppSpacing.gapHorizontalMd,
                            Expanded(
                              child: _buildQuickActionCard(
                                icon: Icons.bed,
                                title: 'Sleep',
                                color: Color(0xFF8B5CF6),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            AppSpacing.gapVerticalXl,
          ],
        ),
      ),
    );
  }

  Widget _buildStreakBadge(String value, String label, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: AppSpacing.borderRadiusMd,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: AppSpacing.iconSm,
          ),
          AppSpacing.gapHorizontalSm,
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCard({
    required IconData icon,
    required String value,
    required String label,
    required double progress,
    required Color color,
    required Gradient gradient,
  }) {
    return GlassMorphismCard(
      borderRadius: 16,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: AppSpacing.paddingSm,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: AppSpacing.borderRadiusSm,
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: AppSpacing.iconSm,
                ),
              ),
              Text(
                '${(progress * 100).toInt()}%',
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          AppSpacing.gapVerticalMd,
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          AppSpacing.gapVerticalMd,
          ClipRRect(
            borderRadius: AppSpacing.borderRadiusSm,
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: color.withOpacity(0.1),
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required Color color,
  }) {
    return GlassMorphismCard(
      borderRadius: 16,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
        },
        borderRadius: AppSpacing.borderRadiusLg,
        child: Padding(
          padding: AppSpacing.paddingLg,
          child: Column(
            children: [
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      color,
                      color.withOpacity(0.7),
                    ],
                  ),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              AppSpacing.gapVerticalMd,
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon with gradient background
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  gradient: AppColorPalette.primaryGradient,
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      color: AppColorPalette.primaryOrange.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.celebration,
                  color: Colors.white,
                  size: 60,
                ),
              ),
              const SizedBox(height: 32),
              Text(
                'All Caught Up!',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'You\'ve completed all your workouts.\nGreat job staying consistent!',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.white.withOpacity(0.8),
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              // Check for new workouts button
              GlassMorphismCard(
                borderRadius: 16,
                child: InkWell(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    // Refresh the workout data with staggered invalidation
                    Future.delayed(const Duration(milliseconds: 100), () {
                      ref.invalidate(todayWorkoutProvider);
                    });
                    Future.delayed(const Duration(milliseconds: 200), () {
                      ref.invalidate(allWorkoutsProvider);
                    });
                  },
                  borderRadius: BorderRadius.circular(16),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.add,
                          color: AppColorPalette.primaryOrange,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Check for New Workouts',
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppColorPalette.primaryOrange,
              strokeWidth: 3,
            ),
            const SizedBox(height: 24),
            Text(
              'Loading your fitness journey...',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good morning! 🌅';
    } else if (hour < 17) {
      return 'Good afternoon! ☀️';
    } else {
      return 'Good evening! 🌙';
    }
  }

  String _getMotivationalText() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Ready to start strong today?';
    } else if (hour < 17) {
      return 'Keep the momentum going!';
    } else {
      return 'One more push to greatness!';
    }
  }
}
