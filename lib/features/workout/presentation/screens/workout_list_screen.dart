import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/spacing.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../dashboard/data/repositories/dashboard_repository.dart';
import '../../../dashboard/domain/models/today_workout.dart';
import 'workout_hero_details_screen.dart';

// Provider for workout list
final workoutListProvider = FutureProvider.autoDispose<List<TodayWorkout>>((ref) async {
  final repository = DashboardRepository();
  return repository.getAllWorkouts();
});

class WorkoutListScreen extends ConsumerWidget {
  const WorkoutListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final workoutListAsync = ref.watch(workoutListProvider);

    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      appBar: AppBar(
        title: Text(
          'Your Workouts',
          style: AppTypography.displayNumbers(
            fontSize: 20,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColorPalette.darkBackground,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
        ),
        actions: [
          IconButton(
            onPressed: () {
              // Refresh workouts with debouncing to prevent rapid invalidations
              Future.delayed(const Duration(milliseconds: 100), () {
                ref.invalidate(workoutListProvider);
              });
            },
            icon: const Icon(Icons.refresh, color: Colors.white),
          ),
        ],
      ),
      body: workoutListAsync.when(
        data: (workouts) => _buildWorkoutList(context, workouts),
        loading: () => _buildLoadingState(),
        error: (error, stack) => _buildErrorState(error.toString()),
      ),
    );
  }

  Widget _buildWorkoutList(BuildContext context, List<TodayWorkout> workouts) {
    if (workouts.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      key: const ValueKey('workout_list'),
      padding: const EdgeInsets.all(20),
      itemCount: workouts.length,
      itemBuilder: (context, index) {
        final workout = workouts[index];
        return Container(
          key: ValueKey('workout_container_${workout.id}'),
          child: Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildWorkoutCard(context, workout),
          ),
        );
      },
    );
  }

  Widget _buildWorkoutCard(BuildContext context, TodayWorkout workout) {
    return GlassMorphismCard(
      child: InkWell(
        onTap: () => _navigateToWorkoutDetails(context, workout),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColorPalette.primaryOrange.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColorPalette.primaryOrange.withOpacity(0.5),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.fitness_center,
                      color: AppColorPalette.primaryOrange,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          workout.name,
                          style: AppTypography.displayNumbers(
                            fontSize: 18,
                            color: Colors.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${workout.exercises.length} exercises • ${workout.totalSets} sets',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Colors.white.withOpacity(0.5),
                    size: 24,
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Workout stats
              Row(
                children: [
                  _buildStatChip(
                    icon: Icons.timer,
                    value: '${workout.estimatedDuration}min',
                    label: 'Duration',
                  ),
                  const SizedBox(width: 12),
                  _buildStatChip(
                    icon: Icons.local_fire_department,
                    value: '${workout.estimatedCalories}',
                    label: 'Calories',
                  ),
                ],
              ),
              
              if (workout.description.isNotEmpty) ...[
                const SizedBox(height: 12),
                Text(
                  workout.description,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14,
                    height: 1.4,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              
              if (workout.isCompleted) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.green.withOpacity(0.5),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Completed',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatChip({
    required IconData icon,
    required String value,
    required String label,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: AppColorPalette.primaryOrange,
            size: 14,
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppColorPalette.primaryOrange,
          ),
          const SizedBox(height: 16),
          Text(
            'Loading workouts...',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading workouts',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.fitness_center,
              color: Colors.white.withOpacity(0.5),
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'No workouts found',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first workout to get started',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToWorkoutDetails(BuildContext context, TodayWorkout workout) {
    // Keep using Navigator.push for now as WorkoutHeroDetailsScreen 
    // is a modal-style screen without a dedicated route
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WorkoutHeroDetailsScreen(workout: workout),
      ),
    );
  }

}
